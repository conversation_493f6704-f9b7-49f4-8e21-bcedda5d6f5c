import pandas as pd
from pymongo import MongoClient
import os
from bson import ObjectId

from loggerfile import logger
from config_manager import ConfigManager


config = ConfigManager()
URL = config.get_value("MONGO_CREDENTIAL", "URL")


def db_connection(db_name):
    """Establish a connection to MongoDB and return the client and collection."""
    client = MongoClient(URL)
    db = client[db_name]
    return client, db


def handle_missing_values(df):
    """Handle missing values by replacing NAType with None for MongoDB compatibility."""
    return df.where(pd.notna(df), None)  # Replace NA with None


def insert_data_into_mongodb(db_name, df, coll_name, update=False, update_field=None):
    """
    Insert or update data from DataFrame into MongoDB collection.
    :param db_name: Database name
    :param df: DataFrame to insert/update
    :param coll_name: Collection name
    :param update: If True, update existing documents based on _id
    :param update_field: Field to update (e.g., 'Product')
    """
    logger.info(f"{'Updating' if update else 'Inserting'} {len(df)} records into {coll_name}")
    try:
        client, db = db_connection(db_name)
        collection = db[coll_name]
        if df.empty:
            logger.info(f"No data to process for {coll_name}")
            client.close()
            return []
            
        # Handle missing values before inserting into MongoDB
        df_cleaned = handle_missing_values(df)
        # Convert the DataFrame to a list of dictionaries (records)
        records = df_cleaned.to_dict(orient='records')

        if update:
            # Update existing documents based on _id
            for record in records:
                if '_id' in record and isinstance(record['_id'], str):
                    record['_id'] = ObjectId(record['_id'])
                update_data = {
                    update_field: record[update_field],  # Entire section (e.g., Product with last_updated)
                    'timestamp': record['timestamp']     # Root timestamp
                } if update_field else record
                collection.update_one(
                    {'_id': record['_id']},
                    {'$set': update_data},
                    upsert=True
                )
            logger.info(f"Updated {len(records)} records in {coll_name}")
        else:
            # Insert new documents
            result = collection.insert_many(records, ordered=False)
            logger.info(f"Inserted {len(records)} rows into {coll_name} collection")
            client.close()
            return result.inserted_ids
        
        client.close()
        return []
    except Exception as e:
        logger.error(f"An error occurred while {'updating' if update else 'inserting'} data into MongoDB collection {coll_name}: {e}")
        return []


def read_data_from_mongo(db_name, coll_name):
    logger.info(f"Reading data from MongoDB collection {coll_name}")
    try:
        client, db = db_connection(db_name)  # Establish a connection to the MongoDB database
        collection = db[coll_name]  # Access the specified collection

        # Fetch all documents from the collection
        data = list(collection.find())
        # Close the MongoDB connection
        client.close()

        # Convert the data into a DataFrame
        # MongoDB documents may contain an "_id" field, which is usually not JSON serializable,
        # so we drop or format it if necessary.
        product_df = pd.DataFrame(data)
        if "_id" in product_df.columns:
            product_df["_id"] = product_df["_id"].astype(str)  # Convert "_id" to string if needed
        return product_df
    except Exception as e:
        logger.error(f"An error occurred while reading data from MongoDB collection {coll_name}: {e}")
        return pd.DataFrame()
