import sys
import math
import pandas as pd
import pyodbc
import warnings
from bson import ObjectId
from datetime import datetime, timezone

from loggerfile import logger
from config_manager import ConfigManager
from mongodb import read_data_from_mongo, insert_data_into_mongodb
from warehouse import get_supplier_warehouse_details


class DataProcessor:
    def __init__(self, server, database, username, password, oms_base_url, request_mongo_dbname):
        warnings.filterwarnings("ignore")
        self.initialize_request()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.OMSBaseURL = oms_base_url
        self.request_mongo_dbname = request_mongo_dbname
        
        self.temp_data_mongo_dbname = sys.argv[1]
        self.conn = None
        self.Req_id = sys.argv[5]
        self.SelectedDate = sys.argv[7]
        self.Type = int(sys.argv[4])
        self.stock_df = pd.DataFrame()
        self.sps_df = pd.DataFrame()
        self.SupplierId = sys.argv[6]
        self.match_stock_df = pd.DataFrame()
        self.cat_match_in_spd = pd.DataFrame()
        self.not_match_cat_in_spd = pd.DataFrame()
        self.product_id_match_in_sps = pd.DataFrame()
        self.product_id_not_match_in_sps = pd.DataFrame()
        self.size_df = pd.DataFrame()
        self.warehouse_df = pd.DataFrame()

    def initialize_request(self):
        self.Req_id = sys.argv[5]
        self.SelectedDate = sys.argv[7]
        self.Type = int(sys.argv[4])
        self.stock_df = pd.DataFrame()
        self.sps_df = pd.DataFrame()
        self.SupplierId = sys.argv[6]
        self.match_stock_df = pd.DataFrame()
        self.cat_match_in_spd = pd.DataFrame()
        self.not_match_cat_in_spd = pd.DataFrame()
        self.product_id_match_in_sps = pd.DataFrame()
        self.product_id_not_match_in_sps = pd.DataFrame()
        self.size_df = pd.DataFrame()
        self.warehouse_df = pd.DataFrame()

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def get_db_counts(self, supplier_id):
        logger.info("get_db_counts")
        try:
            self.connect_to_database()
            query = """
                SELECT 
                    COUNT(*) as total_stocks,
                    SUM(CASE WHEN sp.OmsEchemPortalSupplierId = ? THEN 1 ELSE 0 END) as supplier_stocks
                FROM [dbo].[SupplierProductStocks] sps
                JOIN [dbo].[SupplierProducts] sp ON sps.SupplierProductId = sp.SupplierProductId
            """
            cursor = self.conn.cursor()
            cursor.execute(query, (int(supplier_id),))  # Parameterized query
            result = cursor.fetchone()
            if result is None:
                logger.error("Query returned no results")
                return (0, 0)
            return (
                int(result[0]),  # total_stocks
                int(result[1])   # supplier_stocks
            )
        except Exception as e:
            logger.error(f"Error executing get_db_counts: {e}")
            return (0, 0)
        finally:
            if 'cursor' in locals():
                cursor.close()
            if self.conn:
                self.conn.close()

    def read_data_from_supplier_product(self):
        logger.info("read_data_from_supplier_product")
        self.connect_to_database()

        # Extract catalog IDs from product_df
        catalog_ids = self.stock_df['SupplierCatalogId'].tolist()
        supplier_index = self.stock_df['SupplierId'].first_valid_index()
        supplier_id = self.stock_df.at[supplier_index, 'SupplierId'] if supplier_index is not None else None

        batch_size = 1000
        total_ids = len(catalog_ids)
        total_batches = math.ceil(total_ids / batch_size)

        result_df = pd.DataFrame()

        for i in range(total_batches):
            batch_ids = catalog_ids[i * batch_size: (i + 1) * batch_size]
            batch_ids_str = ", ".join(f"'{str(id)}'" for id in batch_ids)
            query = f"""SELECT * FROM [dbo].[SupplierProducts]
            WHERE SupplierCatalogId IN ({batch_ids_str})
            and OmsEchemPortalSupplierId = '{supplier_id}'"""

            batch_df = pd.read_sql(query, self.conn)
            result_df = pd.concat([result_df, batch_df], ignore_index=True)
            logger.info(f"Processed batch {i + 1}/{total_batches} - {len(batch_df)} records fetched.")

        self.cat_match_in_spd = result_df

        # Merge the two dataframes to find matching rows and include SupplierProductId from cat_match_in_spd
        exists_spd_df = self.stock_df.merge(self.cat_match_in_spd[['SupplierCatalogId', 'SupplierProductId']],
                                            on='SupplierCatalogId', how='inner')
        self.not_match_cat_in_spd = self.stock_df[
            ~self.stock_df['SupplierCatalogId'].isin(self.cat_match_in_spd['SupplierCatalogId'])]

        self.conn.close()
        return exists_spd_df

    def read_data_from_supplier_product_stock(self, exists_stocks):
        logger.info("read_data_from_supplier_product_stock")
        self.connect_to_database()
        batch_size = 1000
        catalog_ids = exists_stocks['SupplierProductId'].tolist()
        total_ids = len(catalog_ids)
        total_batches = math.ceil(total_ids / batch_size)
        result_df = pd.DataFrame()

        for i in range(total_batches):
            batch_ids = catalog_ids[i * batch_size: (i + 1) * batch_size]
            batch_ids_str = ", ".join(f"'{str(id)}'" for id in batch_ids)
            query = f"""SELECT sps.*, su.SizeUnit
                        FROM [dbo].[SupplierProductStocks] sps
                        INNER JOIN [dbo].[SizeUnits] su ON sps.SizeUnitId = su.SizeUnitId
                        WHERE SupplierProductId IN ({batch_ids_str})"""
            batch_df = pd.read_sql(query, self.conn)
            result_df = pd.concat([result_df, batch_df], ignore_index=True)
            logger.info(f"Processed batch {i + 1}/{total_batches}")

        self.sps_df = result_df
        self.conn.close()

    def read_data_from_warehouse(self):
        supplier_warehouse_details = get_supplier_warehouse_details(self.SupplierId, self.OMSBaseURL)
        self.warehouse_df = pd.DataFrame(supplier_warehouse_details.get('data', []))

        if not self.warehouse_df.empty and {'supplierWarehouseId', 'warehouseSheetName', 'supplierId'}.issubset(
                self.warehouse_df.columns):
            column_mapping = {
                'supplierWarehouseId': 'WarehouseId',
                'warehouseSheetName': 'WarehouseName',
                'supplierId': 'SupplierId'
            }
            self.warehouse_df.rename(columns=column_mapping, inplace=True)
        else:
            self.warehouse_df = pd.DataFrame(columns=['WarehouseId', 'WarehouseName', 'SupplierId'])

    def read_data_from_size(self):
        logger.info("read_data_from_size")
        self.connect_to_database()
        query = 'select * from SizeUnits'
        self.size_df = pd.read_sql(query, self.conn)
        self.conn.close()

    def map_product_id_in_sps(self, exists_stock):
        logger.info("map_product_id_in_sps")

        self.product_id_match_in_sps = exists_stock.merge(
            self.sps_df[['SupplierProductId', 'LastUpdatedAt']],
            on='SupplierProductId',
            how='inner'
        )
        self.product_id_match_in_sps = self.product_id_match_in_sps.sort_values("LastUpdatedAt", ascending=False)
        self.product_id_match_in_sps = self.product_id_match_in_sps.drop_duplicates(
            subset=['SupplierProductId', 'StockQty', 'StockUnit', 'WarehouseId'],
            keep='first'
        )
        # self.product_id_match_in_sps = exists_stock[
        #     exists_stock['SupplierProductId'].isin(self.sps_df['SupplierProductId'])]
        self.product_id_not_match_in_sps = exists_stock[
            ~exists_stock['SupplierProductId'].isin(self.sps_df['SupplierProductId'])]

    # def check_stock_changes(self):
    #     logger.info("check_stock_changes")
    #     # DataFrames to store results
    #     stock_change_df = []
    #     new_stock_size_df = []
    #     audit_stock_df = []
    #     disable_stock_df = []  # DataFrame to store disabled (removed) stock
    #
    #     # Ensure numeric data types for correct comparisons
    #     self.product_id_match_in_sps['StockQty'] = pd.to_numeric(self.product_id_match_in_sps['StockQty'], errors='coerce')
    #     self.sps_df['StockAmount'] = pd.to_numeric(self.sps_df['StockAmount'], errors='coerce')
    #
    #     # Sort the DataFrames for easier comparisons
    #     self.product_id_match_in_sps = self.product_id_match_in_sps.sort_values(by=['StockQty', 'StockUnit'])
    #     print("Sorted product_id_match_in_spp:")
    #     print(self.product_id_match_in_sps[['StockQty', 'StockUnit', 'WarehouseId']])
    #
    #     self.sps_df = self.sps_df.sort_values(by=['StockAmount', 'SizeUnit'])
    #     print("Sorted spp_df:")
    #     print(self.sps_df[['StockAmount', 'SizeUnit', 'OmsEchemPortalWarehouseId', 'IsDisable']])
    #
    #     # Iterate through each row in product_id_match_in_sps
    #     for _, row in self.product_id_match_in_sps.iterrows():
    #         # Match SupplierProductId and WarehouseID
    #         matching_rows = self.sps_df[
    #             (self.sps_df['SupplierProductId'] == row['SupplierProductId']) &
    #             (self.sps_df['OmsEchemPortalWarehouseId'] == row['WarehouseId'])
    #             ]
    #
    #         # Get the corresponding SizeID from size_df based on Unit
    #         size_row = self.size_df[self.size_df['SizeUnit'] == row['StockUnit']]
    #         size_id = size_row['SizeUnitId'].values[0] if not size_row.empty else None
    #
    #         if not matching_rows.empty:
    #             # If StockUnit is different, treat it as a stock change
    #             for _, matched_row in matching_rows.iterrows():
    #                 if row['StockUnit'] != matched_row['SizeUnit']:
    #                     # This is considered a stock change, not a new stock
    #                     stock_change_df.append({
    #                         'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                         'WarehouseId': matched_row['OmsEchemPortalWarehouseId'],
    #                         'WarehouseName': matched_row['OmsEchemPortalWarehouseName'],
    #                         'SupplierProductId': matched_row['SupplierProductId'],
    #                         'StockAmount': row['StockQty'],
    #                         'SizeUnitId': size_id,
    #                     })
    #
    #                     # Add to audit_stock_df for stock change
    #                     audit_stock_df.append({
    #                         'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                         'OldStockAmount': matched_row['StockAmount'],
    #                         'NewStockAmount': row['StockQty'],
    #                         'OldSizeID': matched_row['SizeUnitId'],
    #                         'NewSizeID': size_id,
    #                     })
    #                 elif row['StockQty'] != matched_row['StockAmount']:
    #                     # If StockUnit is same but quantity differs, it's a stock update
    #                     stock_change_df.append({
    #                         'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                         'WarehouseId': matched_row['OmsEchemPortalWarehouseId'],
    #                         'WarehouseName': matched_row['OmsEchemPortalWarehouseName'],
    #                         'SupplierProductId': matched_row['SupplierProductId'],
    #                         'StockAmount': row['StockQty'],
    #                         'SizeUnitId': size_id,
    #                         'IsDisable': 0
    #                     })
    #
    #                     # Add to audit_stock_df for stock update
    #                     audit_stock_df.append({
    #                         'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                         'OldStockAmount': matched_row['StockAmount'],
    #                         'NewStockAmount': row['StockQty'],
    #                         'OldSizeID': matched_row['SizeUnitId'],
    #                         'NewSizeID': size_id,
    #                     })
    #                 # If the stock was disabled previously, enable it (IsDisable = 0)
    #                 if matched_row['IsDisable']:
    #                     disable_stock_df.append({
    #                         'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                         'WarehouseId': matched_row['OmsEchemPortalWarehouseId'],
    #                         'WarehouseName': matched_row['OmsEchemPortalWarehouseName'],
    #                         'SupplierProductId': matched_row['SupplierProductId'],
    #                         'StockAmount': row['StockQty'],
    #                         'SizeUnitId': size_id,
    #                         'IsDisable': 0  # Enable the stock again
    #                     })
    #         else:
    #             # No match found, treat as new stock (check if SupplierProductId and WarehouseID are different)
    #             new_stock_size_df.append({
    #                 'SupplierCatalogId': row['SupplierCatalogId'],
    #                 'WarehouseId': row['WarehouseId'],
    #                 'WarehouseName': row['WarehouseName'],
    #                 'SupplierProductId': row['SupplierProductId'],
    #                 'StockAmount': row['StockQty'],
    #                 'SizeUnitId': size_id,  # No corresponding SizeID found
    #                 'SupplierId': row['SupplierId']
    #             })
    #
    #     # Handle the case where stocks are removed (present in sps_df but not in product_id_match_in_sps)
    #     for _, matched_row in self.sps_df.iterrows():
    #         matching_row = self.product_id_match_in_sps[
    #             (self.product_id_match_in_sps['SupplierProductId'] == matched_row['SupplierProductId']) &
    #             (self.product_id_match_in_sps['WarehouseId'] == matched_row['OmsEchemPortalWarehouseId'])
    #             ]
    #
    #         # If no match is found, this is a removed stock
    #         if matching_row.empty:
    #             disable_stock_df.append({
    #                 'SupplierProductStockId': matched_row['SupplierProductStockId'],
    #                 'WarehouseId': matched_row['OmsEchemPortalWarehouseId'],
    #                 'WarehouseName': matched_row['OmsEchemPortalWarehouseName'],
    #                 'SupplierProductId': matched_row['SupplierProductId'],
    #                 'StockAmount': matched_row['StockAmount'],
    #                 'SizeUnitId': matched_row['SizeUnitId'],
    #                 'IsDisable': 1  # Mark as disabled
    #             })
    #
    #     # Convert results to DataFrames
    #     stock_change_df = pd.DataFrame(stock_change_df, columns=[
    #         'SupplierProductStockId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
    #         'SizeUnitId', 'IsDisable', 'WarehouseName'
    #     ])
    #     new_stock_size_df = pd.DataFrame(new_stock_size_df, columns=[
    #         'SupplierCatalogId', 'WarehouseId', 'SupplierProductId', 'StockAmount', 'SizeUnitId', 'WarehouseName', 'SupplierId'
    #     ])
    #     audit_stock_df = pd.DataFrame(audit_stock_df, columns=[
    #         'SupplierProductStockId', 'OldStockAmount', 'NewStockAmount', 'OldSizeID', 'NewSizeID'
    #     ])
    #     disable_stock_df = pd.DataFrame(disable_stock_df, columns=[
    #         'SupplierProductStockId', 'WarehouseId', 'SupplierProductId', 'StockAmount', 'SizeUnitId', 'IsDisable', 'WarehouseName'
    #     ])
    #
    #     return stock_change_df, new_stock_size_df, audit_stock_df, disable_stock_df

    def check_stock_changes(self, audit_only=False):
        logger.info("check_stock_changes")

        # Convert numeric columns for proper comparison
        self.product_id_match_in_sps['StockQty'] = pd.to_numeric(self.product_id_match_in_sps['StockQty'],
                                                                 errors='coerce')
        self.sps_df['StockAmount'] = pd.to_numeric(self.sps_df['StockAmount'], errors='coerce')

        # Convert unit columns to string for consistency
        self.product_id_match_in_sps['StockUnit'] = self.product_id_match_in_sps['StockUnit'].astype(str)
        self.sps_df['SizeUnit'] = self.sps_df['SizeUnit'].astype(str)

        # Fetch SizeUnitId mapping once to avoid repeated lookups
        size_df = self.size_df[['SizeUnit', 'SizeUnitId']].set_index('SizeUnit')
        self.product_id_match_in_sps['SizeUnitId'] = self.product_id_match_in_sps['StockUnit'].map(
            size_df['SizeUnitId'])
        print("Sorted product_id_match_in_spp:")
        print(self.product_id_match_in_sps[['StockQty', 'StockUnit', 'WarehouseId']])

        self.sps_df = self.sps_df.sort_values(by=['StockAmount', 'SizeUnit'])
        print("Sorted spp_df:")
        print(self.sps_df[['StockAmount', 'SizeUnit', 'OmsEchemPortalWarehouseId', 'IsDisable']])

        # Merge based on SupplierProductId and WarehouseId
        merged_df = pd.merge(
            self.product_id_match_in_sps,
            self.sps_df,
            left_on=['SupplierProductId', 'WarehouseId', 'StockUnit'],
            right_on=['SupplierProductId', 'OmsEchemPortalWarehouseId', 'SizeUnit'],
            how='left',
            suffixes=('_product', '_sps')
        )

        # Masks for stock changes, new stock entries, and removals
        stock_changed_mask = (
                                     merged_df['StockQty'] != merged_df['StockAmount']
                             ) & (
                                     merged_df['StockUnit'] == merged_df['SizeUnit']
                             )

        new_stock_mask = merged_df['StockAmount'].isna()  # New stock items

        # Create DataFrame for stock changes
        stock_change_df = merged_df[stock_changed_mask].copy()
        stock_change_df = stock_change_df[[
            'SupplierProductStockId', 'OmsEchemPortalWarehouseId', 'SupplierProductId', 'StockQty',
            'SizeUnitId_product', 'OmsEchemPortalWarehouseName'
        ]]
        stock_change_df.columns = ['SupplierProductStockId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
                                   'SizeUnitId', 'WarehouseName']
        stock_change_df['IsDisable'] = 0

        # Create DataFrame for audit stock changes
        if audit_only:

            matched_audit_df = pd.merge(
                self.product_id_match_in_sps,
                self.sps_df,
                left_on=['SupplierProductId', 'WarehouseId', 'StockUnit'],
                right_on=['SupplierProductId', 'OmsEchemPortalWarehouseId', 'SizeUnit'],
                how='inner'
            )

            audit_stock_df = matched_audit_df[[
                'SupplierProductId', 'StockQty', 'SupplierCatalogId',  # 'Price_x' from product_id_match_in_spp
            ]].copy()
            audit_stock_df['StockAmount'] = audit_stock_df['StockQty']  # new value
            audit_stock_df['CreatedAt'] = pd.to_datetime(self.SelectedDate)
            audit_stock_df['SupplierProductStockId'] = matched_audit_df['SupplierProductStockId']
        else:
            audit_stock_df = merged_df[stock_changed_mask].copy()
            audit_stock_df['StockAmount'] = audit_stock_df['StockAmount']  # old value
            audit_stock_df['CreatedAt'] = audit_stock_df['LastUpdatedAt_sps']
            audit_stock_df = audit_stock_df[['SupplierProductStockId', 'StockAmount', 'SupplierCatalogId', 'CreatedAt', 'SupplierProductId']]

        # Create DataFrame for new stock sizes
        if audit_only:
            new_stock_size_df = merged_df[new_stock_mask].copy()
            new_stock_size_df['IsDisable'] = 1
            new_stock_size_df = new_stock_size_df[[
                'SupplierCatalogId', 'WarehouseId', 'SupplierProductId', 'StockQty', 'SizeUnitId_product', 'WarehouseName', 'SupplierId', 'IsDisable'
            ]]
            new_stock_size_df.columns = ['SupplierCatalogId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
                                         'SizeUnitId', 'WarehouseName', 'SupplierId', 'IsDisable']
        else:
            new_stock_size_df = merged_df[new_stock_mask].copy()
            new_stock_size_df['IsDisable'] = 0
            new_stock_size_df = new_stock_size_df[[
                'SupplierCatalogId', 'WarehouseId', 'SupplierProductId', 'StockQty', 'SizeUnitId_product', 'WarehouseName',
                'SupplierId', 'IsDisable'
            ]]
            new_stock_size_df.columns = ['SupplierCatalogId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
                                         'SizeUnitId', 'WarehouseName', 'SupplierId', 'IsDisable']

        # Identify removed (to disable) stock entries
        disable_stock_df = self.sps_df[
            ~self.sps_df.set_index(['SupplierProductId', 'OmsEchemPortalWarehouseId', 'SizeUnit']).index.isin(
                self.product_id_match_in_sps.set_index(['SupplierProductId', 'WarehouseId', 'StockUnit']).index
            )
        ].copy()
        disable_stock_df['IsDisable'] = 1
        disable_stock_df = disable_stock_df[[
            'SupplierProductStockId', 'OmsEchemPortalWarehouseId', 'SupplierProductId', 'StockAmount', 'SizeUnitId',
            'OmsEchemPortalWarehouseName', 'IsDisable'
        ]]
        disable_stock_df.columns = ['SupplierProductStockId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
                                    'SizeUnitId', 'WarehouseName', 'IsDisable']

        re_enable_stock_df = self.sps_df[
            (self.sps_df['IsDisable'] == 1) &
            self.sps_df.set_index(['SupplierProductId', 'StockAmount', 'SizeUnit', 'OmsEchemPortalWarehouseId']).index.isin(
                self.product_id_match_in_sps.set_index(['SupplierProductId', 'StockQty', 'StockUnit', 'WarehouseId']).index)].copy()
        re_enable_stock_df['IsDisable'] = 0  # Re-enable the price entry
        re_enable_stock_df.drop(columns=['CreatedAt', 'UpdatedAt', 'UpdatedBy', 'SizeUnit'], inplace=True)
        re_enable_stock_df = re_enable_stock_df[[
            'SupplierProductStockId', 'OmsEchemPortalWarehouseId', 'SupplierProductId', 'StockAmount', 'SizeUnitId',
            'OmsEchemPortalWarehouseName', 'IsDisable'
        ]]
        re_enable_stock_df.columns = ['SupplierProductStockId', 'WarehouseId', 'SupplierProductId', 'StockAmount',
                                      'SizeUnitId', 'WarehouseName', 'IsDisable']

        return stock_change_df, new_stock_size_df, audit_stock_df, disable_stock_df, re_enable_stock_df

    def read_unit_map_from_unit_mappings(self):
        self.connect_to_database()
        if not self.conn:
            raise pyodbc.DatabaseError("Database connection failed")
        cursor = self.conn.cursor()
        cursor.execute("SELECT Variation, Standard FROM SizeUnitNormalization WHERE IsActive = 1")
        rows = cursor.fetchall()
        if self.conn:
            self.conn.close()
        return {variation.strip().lower(): standard.strip().upper() for variation, standard in rows}

    def normalize_unit(self, unit, unit_map):
        if not isinstance(unit, str):
            return unit
        return unit_map.get(unit.strip().lower(), unit)

    def convert_stock_units(self, df):
        unit_map = self.read_unit_map_from_unit_mappings()

        self.connect_to_database()
        cursor = self.conn.cursor()
        cursor.execute("SELECT SizeUnitId, SizeUnit, BaseSizeUnitId FROM SizeUnits")
        size_units = {row[1]: (row[0], row[2]) for row in cursor.fetchall()}  # {SizeUnit: (SizeUnitId, BaseSizeUnitId)}

        # Fetch all conversion factors in one query
        cursor.execute("SELECT FromSizeUnitId, ToSizeUnitId, ConversionFactor FROM SizeUnitConversions")
        conversion_factors = {(row[0], row[1]): row[2] for row in
                              cursor.fetchall()}  # {(FromSizeUnitId, ToSizeUnitId): ConversionFactor}

        self.conn.close()

        def convert_row(qty, unit):
            if not qty or not unit:
                return qty, unit  # Skip missing values

            normalized_unit = self.normalize_unit(unit, unit_map)
            if normalized_unit not in size_units:
                return qty, unit  # Skip unknown units

            from_unit_id, base_unit_id = size_units[normalized_unit]
            if base_unit_id and (from_unit_id, base_unit_id) in conversion_factors:
                qty *= conversion_factors[(from_unit_id, base_unit_id)]
                normalized_unit = next((k for k, v in size_units.items() if v[0] == base_unit_id),
                                       unit)  # Get base unit name

            return qty, normalized_unit

        # Apply conversion using vectorized operations
        df[['StockQty', 'StockUnit']] = list(map(convert_row, df['StockQty'], df['StockUnit']))

        return df

    def check_existing_report(self, request_id, supplier_id):
        """Check if a report exists for the given request_id and supplier_id."""
        logger.info(f"Checking for report: request_id={request_id}, supplier_id={supplier_id}")
        try:
            reports = read_data_from_mongo(self.request_mongo_dbname, 'request_report')
            if not reports.empty:
                report = reports[
                    (reports['Request ID'] == int(request_id)) &
                    (reports['Supplier ID'] == int(supplier_id))
                ]
                return report.iloc[0].to_dict() if not report.empty else None
            return None
        except Exception as e:
            logger.error(f"Error checking report: {e}")
            return None

    def generate_stock_report_json(self, update_stock_df, new_stock_df, error_stock_count):
        """Generate JSON report for the stock section."""
        logger.info("Generating stock report JSON")
        old_stock = len(self.product_id_match_in_sps)
        new_stock = len(self.product_id_not_match_in_sps)
        total_new_stock = len(new_stock_df)
        inserted = len(new_stock_df)
        updated = len(update_stock_df)
        disabled = len(read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductStocksUpdateTemp'))
        enabled = 1 or len(self.sps_df[self.sps_df['IsDisable'] == 0])

        supplier_id = int(self.stock_df['SupplierId'].iloc[0] if 'SupplierId' in self.stock_df.columns else self.SupplierId)
        supplier_name = self.stock_df['SupplierName'].iloc[0] if 'SupplierName' in self.stock_df.columns else "Test Supplier"

        selected_date = pd.to_datetime(self.SelectedDate).tz_localize(None).tz_localize(timezone.utc).to_pydatetime()

        total_stocks_before, supplier_stocks_before = self.get_db_counts(supplier_id=supplier_id)
        total_stocks_after = total_stocks_before + len(new_stock_df)
        supplier_stocks_after = supplier_stocks_before + len(new_stock_df)

        return {
            "Request Group ID": int(self.Req_id),
            "Request ID": int(self.Req_id),
            "Supplier ID": supplier_id,
            "Supplier Name": supplier_name,

            "Stock": {
                "Stocks Before Request": total_stocks_before,
                "Supplier Stocks Before Request": supplier_stocks_before,
                "Stocks After Request": total_stocks_after,
                "Supplier Stocks After Request": supplier_stocks_after,

                "Total New Stock": total_new_stock,

                "Inserted Stocks": inserted,
                "Updated Stocks": updated,

                "Disabled Stocks": disabled,
                "Enabled Stocks": enabled,

                "last_updated": datetime.now(timezone.utc)
            },
            "timestamp": selected_date
        }

    def process(self):
        self.initialize_request()
        self.stock_df = read_data_from_mongo(self.temp_data_mongo_dbname, sys.argv[2])
        stock_df = self.stock_df.copy().drop_duplicates(
            subset=['SupplierCatalogId', 'StockQty', 'StockUnit', 'Warehouse'], keep='last')

        logger.info(f"Input_Data_Count: {len(stock_df)}")
        stock_df.to_csv(f"{sys.argv[3]}/{self.Req_id}_Input_Data_Count.csv", index=False)
        if not self.stock_df.empty:
            logger.info('Converting Stock Unit into BaseUnit')
            self.stock_df = self.convert_stock_units(self.stock_df)
            self.stock_df['StockQty'] = self.stock_df['StockQty'].astype(float).round(2)
            self.read_data_from_size()
            self.read_data_from_warehouse()

            # Merge dataframes on Warehouse and WarehouseName
            self.warehouse_df['WarehouseName'] = self.warehouse_df['WarehouseName'].astype(str)
            self.stock_df['Warehouse'] = self.stock_df['Warehouse'].astype(str)
            self.stock_df['SupplierId'] = self.stock_df['SupplierId'].astype(int)
            self.stock_df = self.stock_df.merge(self.warehouse_df[['WarehouseId', 'WarehouseName', 'SupplierId']],
                                                left_on=['Warehouse', 'SupplierId'],
                                                right_on=['WarehouseName', 'SupplierId'], how='left')

            warehouse_not_found = self.stock_df[self.stock_df['WarehouseId'].isna()]
            warehouse_not_found['Reason'] = 'Warehouse not found in our database'
            warehouse_not_found = warehouse_not_found[
                warehouse_not_found['StockQty'].notna() | warehouse_not_found['StockUnit'].notna()]
            if not warehouse_not_found.empty:
                insert_data_into_mongodb(self.temp_data_mongo_dbname, warehouse_not_found, f'{self.Req_id}_StockProductDoesNotExists')

            if not self.stock_df.empty:
                if self.Type == 4:
                    exists_spd_df = self.read_data_from_supplier_product()
                    if not self.not_match_cat_in_spd.empty:
                        not_match_cat_in_spd = self.not_match_cat_in_spd[self.not_match_cat_in_spd['StockQty'].notna() |
                                                                         self.not_match_cat_in_spd[
                                                                             'StockUnit'].notna() |
                                                                         self.not_match_cat_in_spd[
                                                                             'WarehouseName'].notna()]
                        not_match_cat_in_spd_2 = not_match_cat_in_spd[
                            ~not_match_cat_in_spd['SupplierCatalogId'].isin(warehouse_not_found['SupplierCatalogId'])]
                        not_match_cat_in_spd_2['Reason'] = 'This Catalog is not in Our SPD table'
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, not_match_cat_in_spd_2,
                                                 f'{self.Req_id}_StockProductDoesNotExists')
                else:
                    exists_spd_df = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_ExistsSupplierProduct')

                if not exists_spd_df.empty:
                    exists_spd_df_unique = exists_spd_df[['SupplierCatalogId', 'SupplierProductId']].drop_duplicates()
                    exists_stocks = self.stock_df.merge(exists_spd_df_unique, on='SupplierCatalogId', how='inner')
                    exists_stocks.drop_duplicates(subset=['SupplierProductId', 'StockQty', 'StockUnit', 'WarehouseId'],
                                                  keep='first', inplace=True)

                    if not exists_stocks.empty:
                        self.read_data_from_supplier_product_stock(exists_stocks)
                        self.map_product_id_in_sps(exists_stocks)

                        selected_date_dt = pd.to_datetime(self.SelectedDate)
                        # Ensure LastUpdatedAt is datetime
                        self.product_id_match_in_sps['LastUpdatedAt'] = pd.to_datetime(self.product_id_match_in_sps['LastUpdatedAt'])

                        # Add a flag column: True if update should happen
                        self.product_id_match_in_sps['ShouldUpdate'] = selected_date_dt > self.product_id_match_in_sps['LastUpdatedAt']

                        # Separate into rows that should be updated vs only audited
                        to_update_df = self.product_id_match_in_sps[self.product_id_match_in_sps['ShouldUpdate'] == True]
                        only_audit_df = self.product_id_match_in_sps[self.product_id_match_in_sps['ShouldUpdate'] == False]

                        logger.info("Exists stock check and insert")
                        if not self.product_id_match_in_sps.empty:
                            temp_exists_stock = self.product_id_match_in_sps.copy()[self.product_id_match_in_sps.copy()['WarehouseId'].notna()]
                            temp_exists_stock.to_csv(f'{sys.argv[3]}/{self.Req_id}_Exists_Data_Count.csv', index=False)

                            # Process updates (real stock changes + audit)
                            if not to_update_df.empty:
                                self.product_id_match_in_sps = to_update_df.copy()
                                stock_change_df, new_stock_size_df, audit_stock_df, disable_stock_df, re_enable_stock_df = self.check_stock_changes()
                                only_audit_ids = only_audit_df['SupplierProductId'].unique()
                                stock_change_df = stock_change_df[~stock_change_df['SupplierProductId'].isin(only_audit_ids)]
                                new_stock_size_df = new_stock_size_df[~new_stock_size_df['SupplierProductId'].isin(only_audit_ids)]
                                audit_stock_df = audit_stock_df[~audit_stock_df['SupplierProductId'].isin(only_audit_ids)]
                                disable_stock_df = disable_stock_df[~disable_stock_df['SupplierProductId'].isin(only_audit_ids)]
                                re_enable_stock_df = re_enable_stock_df[~re_enable_stock_df['SupplierProductId'].isin(only_audit_ids)]

                                if not stock_change_df.empty:
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, stock_change_df, f'{self.Req_id}_SupplierProductStocksUpdateTemp')
                                if not new_stock_size_df.empty:
                                    new_stock_size_df = new_stock_size_df[new_stock_size_df['WarehouseId'].notna()]
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, new_stock_size_df, f'{self.Req_id}_SupplierProductStocksInsertTemp')
                                if not audit_stock_df.empty:
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, audit_stock_df, f'{self.Req_id}_AuditStockInsertTemp')
                                if not disable_stock_df.empty:
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, disable_stock_df, f'{self.Req_id}_SupplierProductStocksUpdateTemp')
                                if not re_enable_stock_df.empty:
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, re_enable_stock_df, f'{self.Req_id}_SupplierProductStocksUpdateTemp')

                            # Process audit-only (ShouldUpdate == False)
                            if not only_audit_df.empty:
                                self.product_id_match_in_sps = only_audit_df.copy()
                                _, new_stock_size_df, audit_stock_df, _, _ = self.check_stock_changes(audit_only=True)
                                to_update_ids = to_update_df['SupplierProductId'].unique()
                                new_stock_size_df = new_stock_size_df[~new_stock_size_df['SupplierProductId'].isin(to_update_ids)]
                                audit_stock_df = audit_stock_df[~audit_stock_df['SupplierProductId'].isin(to_update_ids)]

                                if not new_stock_size_df.empty:
                                    new_stock_size_df = new_stock_size_df[new_stock_size_df['WarehouseId'].notna()]
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, new_stock_size_df, f'{self.Req_id}_SupplierProductStocksInsertTemp')

                                if not audit_stock_df.empty:
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, audit_stock_df, f'{self.Req_id}_AuditStockInsertTemp')

                        logger.info("New Stock check and insert")
                        self.product_id_not_match_in_sps = self.product_id_not_match_in_sps[
                            self.product_id_not_match_in_sps['WarehouseId'].notna()]
                        if not self.product_id_not_match_in_sps.empty:
                            self.product_id_not_match_in_sps = self.product_id_not_match_in_sps.merge(self.size_df,
                                                                                                      left_on='StockUnit',
                                                                                                      right_on='SizeUnit',
                                                                                                      how='left')
                            self.product_id_not_match_in_sps['SizeUnitId'] = self.product_id_not_match_in_sps[
                                'SizeUnitId']
                            self.product_id_not_match_in_sps.drop(columns=['SizeUnit'], inplace=True)
                            self.product_id_not_match_in_sps.rename(columns={'StockQty': 'StockAmount'}, inplace=True)
                            new_df = self.product_id_not_match_in_sps[
                                ['SupplierCatalogId', 'SupplierProductId', 'StockAmount', 'SizeUnitId', 'WarehouseId',
                                 'WarehouseName', 'SupplierId']]
                            new_df['IsDisable'] = 0
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, new_df,
                                                     f'{self.Req_id}_SupplierProductStocksInsertTemp')

                if self.Type == 1:
                    collection_names = [
                        f'{self.Req_id}_SupplierProductDetailsInsertTemp',
                        f'{self.Req_id}_SupplierProductDetailsMappingInsertTemp'
                    ]
                    new_cat_in_spd_1 = read_data_from_mongo(self.temp_data_mongo_dbname, collection_names[0])
                    new_cat_in_spd_2 = read_data_from_mongo(self.temp_data_mongo_dbname, collection_names[1])
                    new_cat_in_spd = pd.concat([new_cat_in_spd_1, new_cat_in_spd_2], ignore_index=True)

                    if not new_cat_in_spd.empty:
                        self.stock_df = self.stock_df.drop('_id', axis=1)
                        new_stock = self.stock_df.merge(new_cat_in_spd[['SupplierCatalogId', '_id']],
                                                        on='SupplierCatalogId', how='inner')

                        if not new_stock.empty:
                            logger.info("New Stock check and insert")
                            new_stock.rename(columns={'_id': 'TempID'}, inplace=True)
                            new_stock = new_stock.merge(self.size_df, left_on='StockUnit', right_on='SizeUnit',
                                                        how='left')
                            new_stock['SizeUnitId'] = new_stock['SizeUnitId']
                            new_stock.drop(columns=['SizeUnit'], inplace=True)
                            new_stock.rename(columns={'StockQty': 'StockAmount'}, inplace=True)
                            new_df = new_stock[
                                ['SupplierCatalogId', 'StockAmount', 'SizeUnitId', 'WarehouseId', 'WarehouseName',
                                 'SupplierId', 'TempID']]
                            new_df = new_df[new_df['WarehouseId'].notna()]
                            new_df['IsDisable'] = 0
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, new_df,
                                                     f'{self.Req_id}_SupplierProductStocksInsertTemp')

                    '''Check Catalog is exists in spd or not'''
                    # Check if both DataFrames are not empty
                    if not exists_spd_df.empty and not new_cat_in_spd.empty:
                        # Merge only when both DataFrames have data
                        merged_df = pd.merge(exists_spd_df, new_cat_in_spd, on='SupplierCatalogId', how='outer')
                        not_stock_cat_in_spd = self.stock_df[
                            ~self.stock_df['SupplierCatalogId'].isin(merged_df['SupplierCatalogId'])]
                        not_stock_cat_in_spd = not_stock_cat_in_spd[not_stock_cat_in_spd['WarehouseId'].notna()]
                        if not not_stock_cat_in_spd.empty:
                            logger.info("This Catalog is not in Our SPD table")
                            not_stock_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, not_stock_cat_in_spd,
                                                     f'{self.Req_id}_StockProductDoesNotExists')

                    # If one DataFrame is empty, use the one that is not empty
                    elif not exists_spd_df.empty:
                        not_stock_cat_in_spd = self.stock_df[
                            ~self.stock_df['SupplierCatalogId'].isin(exists_spd_df['SupplierCatalogId'])]
                        not_stock_cat_in_spd = not_stock_cat_in_spd[not_stock_cat_in_spd['WarehouseId'].notna()]
                        if not not_stock_cat_in_spd.empty:
                            logger.info("This Catalog is not in Our SPD table")
                            not_stock_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, not_stock_cat_in_spd,
                                                     f'{self.Req_id}_StockProductDoesNotExists')

                    elif not new_cat_in_spd.empty:
                        not_stock_cat_in_spd = self.stock_df[
                            ~self.stock_df['SupplierCatalogId'].isin(new_cat_in_spd['SupplierCatalogId'])]
                        not_stock_cat_in_spd = not_stock_cat_in_spd[not_stock_cat_in_spd['WarehouseId'].notna()]
                        if not not_stock_cat_in_spd.empty:
                            logger.info("This Catalog is not in Our SPD table")
                            not_stock_cat_in_spd['Reason'] = 'This Catalog is not in Our SPD table'
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, not_stock_cat_in_spd,
                                                     f'{self.Req_id}_StockProductDoesNotExists')

        '''Read Data From SupplierProductStocksInsertTemp or SupplierProductStocksUpdateTemp or StockProductDoesNotExists'''
        update_product_df = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductStocksUpdateTemp')
        new_product_df = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductStocksInsertTemp')
        error_product_count = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_StockProductDoesNotExists')
        if not update_product_df.empty:
            update_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_Update_Data_Count.csv', index=False)
        if not new_product_df.empty:
            new_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_New_Data_Count.csv', index=False)
        if not error_product_count.empty:
            error_product_count.to_csv(f'{sys.argv[3]}/{self.Req_id}_Error_Data_Count.csv', index=False)

        report_json = self.generate_stock_report_json(update_product_df, new_product_df, error_product_count)

        print("\n\n")
        print("Generated Report JSON:")
        print("=========================================")
        from pprint import pprint
        pprint(report_json, width=120, compact=True, sort_dicts=False, depth=2)
        print("=========================================")
        print("\n\n")
        # input("Press Enter to continue...")

        # Check for existing report
        existing_report = self.check_existing_report(
            self.Req_id,
            supplier_id := report_json['Supplier ID']
        )

        if existing_report:
            logger.info(f"Existing report found for request_id: {self.Req_id}, supplier_id: {supplier_id}. Updating report.")
            report_json['_id'] = ObjectId(existing_report['_id'])
            insert_data_into_mongodb(
                self.request_mongo_dbname,
                pd.DataFrame([report_json]),
                'request_report',
                update=True,
                update_field='Stock'
            )
        else:
            logger.info(f"No existing report found for request_id: {self.Req_id}, supplier_id: {supplier_id}. Inserting new report.")
            insert_data_into_mongodb(self.request_mongo_dbname, pd.DataFrame([report_json]), 'request_report')

if __name__ == "__main__":
    config = ConfigManager()
    
    data = {
        'Server': config.get_value("CREDENTIAL", "SERVER"),
        'Database': config.get_value("CREDENTIAL", "DATABASE"),
        'Username': config.get_value("CREDENTIAL", "USERNAME"),
        'Password': config.get_value("CREDENTIAL", "PASSWORD"),
        'ISProduction': config.get_value("Data", "ISPRODUCTION"),
        'REQUEST_MONGO_DBNAME': config.get_value("MongoDB", "REQUEST_MONGO_DBNAME")
    }
    
    is_production = config.get_value("Data", "ISPRODUCTION").lower() == "true"
    OMSBaseURL = config.get_value("ProductionApi", "OMSBaseURL") if is_production else config.get_value(
        "TestingAPI", "OMSBaseURL")

    print(data)

    data_processor = DataProcessor(
        server=data['Server'],
        database=data['Database'],
        username=data['Username'],
        password=data['Password'],
        oms_base_url=OMSBaseURL,
        request_mongo_dbname=data['REQUEST_MONGO_DBNAME']
    )
    
    data_processor.process()


"""
python client/Scripts/StockImport/main.py EchemNewTest 791_StockExcel /home/<USER>/pythonProjects/data_upload/test/new-echem-data-upload/data-upload-worker/StockProcessQueue/client/Temp/StockImport 1 791 5150 "2025-06-13 08:54:26.639000"

python client/Scripts/StockImport/main.py EchemNewTest 813_StockExcel /home/<USER>/pythonProjects/data_upload/test/new-echem-data-upload/data-upload-worker/StockProcessQueue/client/Temp/StockImport 1 813 5150 "2025-06-25 08:54:26.639000"
"""
