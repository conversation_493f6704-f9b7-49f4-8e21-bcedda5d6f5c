from fastapi import APIRouter, Query, HTTPException, Depends
from datetime import datetime

from app.schemas.request_report_schemas import ReportFilterSchema
from app.services.report_service import fetch_filtered_reports, generate_excel_dynamic
from app.services.db_service import get_db  # adjust if your db dependency import differs

router = APIRouter()

def generate_filename(filters: ReportFilterSchema) -> str:
    """Generate a meaningful filename based on filter criteria"""
    now = datetime.now()
    timestamp = now.strftime("%Y-%m-%d")

    filename = "CustomReport"

    # Add date range to filename
    if filters.start_date and filters.end_date:
        filename += f"_{filters.start_date}_to_{filters.end_date}"
    elif filters.start_date:
        filename += f"_from_{filters.start_date}"
    elif filters.end_date:
        filename += f"_until_{filters.end_date}"

    # Add supplier if specified
    if filters.supplier_id:
        filename += f"_Supplier{filters.supplier_id}"

    # Add request ID if specified
    if filters.request_id:
        filename += f"_Request{filters.request_id}"

    filename += f"_{timestamp}.xlsx"

    return filename

@router.get("/reports")
async def get_or_download_reports(
    filters: ReportFilterSchema = Depends(),
    db = Depends(get_db)
):
    data = await fetch_filtered_reports(
        db,
        filters.start_date,
        filters.end_date,
        filters.week,
        filters.supplier_id,
        filters.request_id,
        filters.request_group_id
    )
    print("\n\n")
    print(data)
    print("\n\n")

    if filters.download:
        # Generate filename if not provided
        filename = filters.filename or generate_filename(filters)
        return await generate_excel_dynamic(data, filename)
    
    return {"status": "success", "data": data}
