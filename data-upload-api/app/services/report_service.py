from openpyxl import Workbook
from openpyxl.styles import Align<PERSON>, PatternFill, Border, Side
from fastapi.responses import StreamingResponse
from io import BytesIO
from typing import List
from datetime import datetime
from fastapi import HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Optional


async def fetch_filtered_reports(
    db: AsyncIOMotorDatabase,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    week: Optional[int] = None,
    supplier_id: Optional[int] = None,
    request_id: Optional[int] = None,
    request_group_id: Optional[str] = None
):
    query = {}

    if start_date and end_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            # Add one day to end_date to include the entire end date
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            query["timestamp"] = {"$gte": start_dt, "$lte": end_dt}
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD.")

    if supplier_id:
        query["supplier_id"] = supplier_id

    if request_id:
        query["request_id"] = request_id

    if request_group_id:
        query["request_group_id"] = request_group_id

    # Use the correct collection name for report data
    collection = db["request_report"]

    print("\n\n")
    print(query)
    print("\n\n")
    
    data = await collection.find(query).to_list(length=None)

    # Convert ObjectId to string for JSON serialization
    for item in data:
        item["_id"] = str(item["_id"])

    return data


def reorder_json(data: list):
    """
    Reorder the JSON fields into non-nested first, nested second.
    This works dynamically for any given structure.
    """
    reordered_data = []
    
    for item in data:
        non_nested = {}
        nested = {}

        # Separate non-nested and nested fields
        for key, value in item.items():
            if isinstance(value, dict):  # If value is a dictionary, it's nested
                nested[key] = value
            else:
                non_nested[key] = value

        # Append the non-nested first, then the nested fields
        reordered_item = {**non_nested, **nested}
        reordered_data.append(reordered_item)

    return reordered_data


def parse_json_headers(data: list):
    """
    Parses a list of dicts and returns:
    - header1: outer keys (flat keys first, then nested dict keys with blanks)
    - header2: sub keys for nested dicts, empty for flat keys
    - merge_ranges: dict of outer_key -> (start_col, end_col) for merging cells
    """
    if not data:
        return [], [], {}, {}

    # Reorder data first (non-nested first, nested second)
    reordered_data = reorder_json(data)

    sample = reordered_data[0]

    header1, header2 = [], []
    merge_ranges = {}
    nested_subkeys_map = {}  # Map nested key to its subkeys

    col_index = 1  # Excel column index starts at 1

    # First pass: collect flat keys and nested keys
    flat_keys = [k for k, v in sample.items() if not isinstance(v, dict)]
    nested_keys = [k for k, v in sample.items() if isinstance(v, dict)]

    # Process flat keys first
    for key in flat_keys:
        header1.append(key)
        header2.append("")
        col_index += 1

    # Process nested dict keys
    for key in nested_keys:
        sub_keys = list(sample[key].keys())
        header1.append(key)
        header2.extend(sub_keys)
        merge_ranges[key] = (col_index, col_index + len(sub_keys) - 1)
        nested_subkeys_map[key] = sub_keys
        col_index += len(sub_keys)

    return header1, header2, merge_ranges, nested_subkeys_map


def generate_pastel_color(index: int) -> str:
    """
    Generate a visually distinct pastel colour based on an index.
    Uses HSL hue rotation for distinguishable colours.
    """
    hue = (index * 45) % 360  # Rotate hue to avoid similar colours
    saturation = 30 + (index * 7) % 30  # Keep pastel (low saturation)
    lightness = 80  # Lightness fixed for pastel

    import colorsys
    r, g, b = colorsys.hls_to_rgb(hue / 360, lightness / 100, saturation / 100)
    return '{:02X}{:02X}{:02X}'.format(int(r * 255), int(g * 255), int(b * 255))


async def generate_excel_dynamic(data: list, filename: str = "dynamic_report.xlsx"):
    if not data:
        raise HTTPException(status_code=404, detail="No data found for report.")

    wb = Workbook()
    ws = wb.active
    ws.title = "Dynamic Report"

    # -----------------
    # PARSE HEADERS
    # -----------------

    print(data)
    header1, header2, merge_ranges, nested_subkeys_map = parse_json_headers(data)

    # Define styles
    header_fill = PatternFill(start_color="B7C7D6", end_color="B7C7D6", fill_type="solid")
    
    dark_border = Border(
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000")
    )

    # category_fills = {
    #     "product": PatternFill(start_color="D9EAD3", end_color="D9EAD3", fill_type="solid"),
    #     "price": PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid"),
    #     "stock": PatternFill(start_color="C9DAF8", end_color="C9DAF8", fill_type="solid"),
    # }

    # Dynamically generate colors for each category
    categories = list(merge_ranges.keys())
    category_fills = {}
    for idx, category in enumerate(categories):
        pastel_color = generate_pastel_color(idx)
        category_fills[category] = PatternFill(start_color=pastel_color, end_color=pastel_color, fill_type="solid")


    # Write header rows
    ws.append(header1)
    ws.append(header2)

    # -----------------
    # MERGE CELLS & STYLE
    # -----------------
    for category, (start_col, end_col) in merge_ranges.items():
        ws.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
        cell = ws.cell(row=1, column=start_col)
        cell.value = category
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.fill = category_fills.get(category, header_fill)
        for row in ws.iter_rows(min_row=1, max_row=1, min_col=start_col, max_col=end_col):
            for c in row:
                c.border = dark_border

    # Style flat keys headers
    for col in range(1, len(header1) + 1):
        if ws.cell(row=2, column=col).value == "":
            cell = ws.cell(row=1, column=col)
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.fill = header_fill
            cell.border = dark_border

    # Style sub-headers
    for col in range(1, len(header2) + 1):
        cell = ws.cell(row=2, column=col)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        for category, (start_col, end_col) in merge_ranges.items():
            if start_col <= col <= end_col:
                cell.fill = category_fills.get(category, header_fill)
        cell.border = dark_border

    # -----------------
    # WRITE DATA ROWS
    # -----------------
    for idx, item in enumerate(reorder_json(data)):
        row = []
        for key in header1:
            if key in item and not isinstance(item[key], dict):
                row.append(item[key])
            elif key in item and isinstance(item[key], dict):
                subkeys = nested_subkeys_map[key]
                row.extend([item[key].get(subkey, "") for subkey in subkeys])
        ws.append(row)
        # Apply alternating row colors & borders
        row_num = idx + 3  # data rows start from row 3
        fill = PatternFill(start_color="F9F9F9", end_color="F9F9F9", fill_type="solid") if row_num % 2 == 0 else PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
        for cell in ws[row_num]:
            cell.fill = fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = dark_border

    # -----------------
    # RETURN STREAMING RESPONSE
    # -----------------
    stream = BytesIO()
    wb.save(stream)
    stream.seek(0)

    return StreamingResponse(
        stream,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
