# data-upload-api/app/services/file_process_service.py

import io
import logging
from pathlib import Path
from datetime import datetime, timezone
import asyncio

import httpx
import pandas as pd
from fastapi import HTTPException
from tenacity import retry, wait_fixed, stop_after_attempt

from app.utils.settings import (
    CLOUD_DOC_BASE_URL,
    BUCKET_UUID,
    REQ_COLLECTION_NAME,
    ROW_LIMIT,
    DEFAULT_STATUS_OBJ,
    DEFAULT_STAGE_OBJ
)
from app.utils.utils import generate_unique_4_digit
from app.utils.rabitmq_client import RabbitMQClient
from app.services.db_service import bulk_save_data_to_db, get_db
from app.services.update_request import update_request_obj


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

collection_data = {
    "collection_data": [
        {"name": "Status", "obj_key": "StatusObj", "mapping_key": ["StatusId", "StatusName"]},
        {"name": "Stages", "obj_key": "StageObj", "mapping_key": ["StageId", "StageName"]},
        {"name": "RequestType", "obj_key": "RequestTypeObj", "mapping_key": ["RequestTypeID", "RequestTypeName"]},
    ]
}

@retry(wait=wait_fixed(2), stop=stop_after_attempt(3))
async def upload_file_to_cloud(df_chunk, file_part_name, file_extension):
    """
    Uploads a file chunk to the cloud storage.

    Args:
        df_chunk (pd.DataFrame): The DataFrame chunk to upload.
        file_part_name (str): The name of the file part to upload.
        file_extension (str): The file extension ('xlsx' or 'csv').

    Returns:
        tuple: A tuple containing the file UUID and the uploaded filename.
    """
    try:
        with io.BytesIO() as output_buffer:
            if file_extension == "csv":
                df_chunk.to_csv(output_buffer, index=False)
            else:
                df_chunk.to_excel(output_buffer, index=False, engine="openpyxl")
            output_buffer.seek(0)
            async with httpx.AsyncClient() as client:
                files = {'file': (file_part_name, output_buffer)}
                res = await client.post(
                    f"{CLOUD_DOC_BASE_URL}/api/external/file/uploadFile/",
                    files=files,
                    params={"bucket_uuid": BUCKET_UUID}
                )
            res.raise_for_status()
            res_data = res.json()
            if res_data['status_code'] == 200:
                return res_data['data']['uuid'], res_data['data']['filename']
            raise ValueError("Error in file upload response.")
    except Exception as e:
        logger.error(f"Error in upload: {str(e)}")
        raise


def get_created_date(created_date_str=None):
    """
    Returns the created date as a UTC datetime object.

    Args:
        created_date_str (str): Optional string representation of a date.

    Returns:
        datetime: The created date in UTC.
    """
    current_time = datetime.now(timezone.utc)
    if created_date_str:
        try:
            selected_date = datetime.strptime(created_date_str, '%Y-%m-%d')
            return selected_date.replace(
                hour=current_time.hour,
                minute=current_time.minute,
                second=current_time.second,
                microsecond=current_time.microsecond,
                tzinfo=timezone.utc
            )
        except ValueError:
            logger.warning("Invalid CreatedDate format. Using current UTC time instead.")
    return current_time


def create_request_data(
    file_uuid, uploaded_filename, created_date, request_group_id, 
    request_type_id, supplier_id, supplier_name
):
    """
    Creates a request data object for file processing.

    Args:
        file_uuid (str): The UUID of the file.
        uploaded_filename (str): The uploaded filename.
        created_date (datetime): The creation date of the request.
        request_group_id (str): The request group ID.
        request_type_id (int): The request type ID.
        supplier_id (str): The supplier ID.
        supplier_name (str): The supplier name.

    Returns:
        dict: The constructed request data.
    """
    return {
        "RequestGroupID": request_group_id,
        "StatusObj": DEFAULT_STATUS_OBJ,
        "StageObj": DEFAULT_STAGE_OBJ,
        "RequestTypeObj": request_type_id,
        "InputFileName": uploaded_filename,
        "SupplierID": supplier_id,
        "SupplierName": supplier_name,
        "FileUUID": file_uuid,
        "RequestTime": None,
        "CreatedDate": created_date,
        "UpdatedDate": datetime.now(timezone.utc),
    }

async def process_excel_file(
    file_path, created_date=None, request_type_id=None, supplier_id=None, 
    supplier_name=None, db=None
):
    """
    Processes an Excel file, uploads parts to the cloud, and stores data in the database.

    Args:
        file_path (str): Path to the Excel file.
        created_date (str): Optional creation date string.
        request_type_id (int): The request type ID.
        supplier_id (str): The supplier ID.
        supplier_name (str): The supplier name.
        db (AsyncIOMotorDatabase): The database client.

    Returns:
        bool: Whether the processing and database insertion was successful.

    Raises:
        HTTPException: If file processing or validation fails.
    """
    try:
        file_path = Path(file_path)
        if file_path.stat().st_size == 0:
            raise ValueError("The uploaded file is empty.")
        if request_type_id is None:
            raise ValueError("RequestTypeId is required.")
        if db is None:
            raise ValueError("Database client is required.")

        file_extension = file_path.suffix.lstrip('.')
        # Read the entire Excel file to get the total number of rows
        df = pd.read_excel(file_path)
        total_rows = len(df)
        if total_rows == 0:
            raise ValueError("The uploaded file is empty.")
        
        num_parts = (total_rows // ROW_LIMIT) + (1 if total_rows % ROW_LIMIT > 0 else 0)
        request_group_id = generate_unique_4_digit() if num_parts > 1 else None

        logger.info(f"Processing file: {file_path}, Total rows: {total_rows}, Number of parts: {num_parts}")

        tasks = []
        for i in range(num_parts):
            start_row = i * ROW_LIMIT
            end_row = min((i + 1) * ROW_LIMIT, total_rows)
            df_chunk = df.iloc[start_row:end_row]

            # Clean filename (remove timestamp)
            clean_stem = file_path.stem.split('_', 1)[1] if '_' in file_path.stem else file_path.stem
            file_part_name = (
                f"{clean_stem}_part{i + 1}{file_path.suffix}" if num_parts > 1 else f"{clean_stem}{file_path.suffix}"
            )

            logger.debug(f"Processing chunk {i + 1}/{num_parts}, Rows: {len(df_chunk)}")
            tasks.append(upload_file_to_cloud(df_chunk, file_part_name, file_extension))

        upload_results = await asyncio.gather(*tasks, return_exceptions=True)

        if db is None:
            logger.error("No database connection provided.")

            return False  # No database connection provided
        else:
            db = get_db()
            batch_data = []
            for idx, result in enumerate(upload_results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to upload part {idx + 1}: {str(result)}")
                    continue
                file_uuid, uploaded_filename = result
                created_date_dt = get_created_date(created_date)
                data = create_request_data(
                    file_uuid, uploaded_filename, created_date_dt, request_group_id,
                    request_type_id, supplier_id, supplier_name
                )
                print(f"Processing data for part {idx + 1}: {data}")
                updated_status_obj = await update_request_obj(data, db, collection_data)
                batch_data.append(updated_status_obj)

            success = await bulk_save_data_to_db(REQ_COLLECTION_NAME, batch_data, db)
            logger.info(f"File processing completed for: {file_path}")
            
            # Send the data to RabbitMQ if the save was successful
            if success:
                publisher = RabbitMQClient(queue_name="request_queue")
                for request_data in batch_data:
                    # Publish each request data to the RabbitMQ queue
                    publisher.publish(request_data)
                logger.info(f"Published {len(batch_data)} requests to RabbitMQ.")
            
            return success

    except ValueError as ve:
        logger.error(f"ValueError: {str(ve)}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
