from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from contextlib import asynccontextmanager
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates

from app.routes.genrate_report import router as genrate_report
from app.routes.file_routes import router as file_router
from app.routes.file_upload import router as file_upload
from app.routes.status_routes import router as status_router
from app.routes.log_routes import router as log_router
from app.routes.remaining_files import router as remaining_files
from app.routes.request_report import router as request_report
from app.routes.output_files import router as output_files
from app.routes.request_router import router as requet_router
from app.routes.login_logout_router import router as login_logout_router
from app.routes.supplier_routes import router as supplier_router
from app.routes.approval_router import router as approval_router
from app.routes.task_monitor_routes import router as task_monitor_router
from app.routes import custom_report_routes

from app.utils.settings import MONGO_URI, GRAPH_URL
from app.services.db_service import MongoDB
from app.services.request_service import get_request_counts, get_data_import_requests
from app.services.supplier_service import get_all_suppliers
from app.services.approval_service import fetch_pending_requests_service

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event for FastAPI (handles startup & shutdown)"""
    print("🚀 Connecting to MongoDB...")
    await MongoDB.connect(MONGO_URI)  # ✅ Initialize MongoDB
    yield  # FastAPI will run the app while keeping DB connection alive
    print("🛑 Closing MongoDB connection...")
    await MongoDB.close()  # ✅ Cleanup on shutdown

app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Or specify the frontend's URL
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Register routers
app.include_router(file_router, prefix="/api/files", tags=["File Management"])
app.include_router(file_upload, prefix="/api/files", tags=["File Management"])
app.include_router(status_router, prefix="/api", tags=["Status Management"])
app.include_router(log_router, prefix="/api", tags=["Log Management"])
app.include_router(remaining_files, prefix="/api", tags=["Remaining File"])
app.include_router(request_report, prefix="/api", tags=["Request Report"])
app.include_router(output_files, prefix="/api", tags=["Output Files"])
app.include_router(genrate_report, prefix="/api", tags=["Genrate Report"])
app.include_router(requet_router, tags=["Request"])
app.include_router(login_logout_router, tags=["LoginLogOut"])
app.include_router(supplier_router, prefix="/api", tags=["Suppliers"])
app.include_router(approval_router, prefix="/api", tags=["Approval"])
app.include_router(task_monitor_router, prefix="/api/monitor", tags=["Task Monitor"])
app.include_router(custom_report_routes.router, prefix="/api")

# Mount static files (CSS, JS, etc.)
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup Jinja2 templates for HTML rendering
templates = Jinja2Templates(directory="templates")

# This function checks if the user is authenticated and returns the user if authenticated
# Otherwise, it redirects to the login page
def get_current_user(request: Request):
    # Check if the "user" cookie exists to identify if the user is authenticated
    user = request.cookies.get("user")
    if not user:
        # If the user is not authenticated, redirect to the login page
        raise HTTPException(status_code=status.HTTP_303_SEE_OTHER, detail="Redirecting to login", headers={"Location": "/login"})
    return user

@app.get("/")
async def home(request: Request, user: str = Depends(get_current_user)):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/index-fixed")
async def home_fixed(request: Request, user: str = Depends(get_current_user)):
    return templates.TemplateResponse("index_fixed.html", {"request": request})

@app.get("/view_requests", response_class=HTMLResponse)
async def view_requests(request: Request, user: str = Depends(get_current_user)):
    # Any additional data you want to pass to the template
    return templates.TemplateResponse("view_requests.html", {"request": request})

@app.get("/approval", response_class=HTMLResponse)
async def approval_page(request: Request, user: str = Depends(get_current_user)):
    """Render the approval management page"""
    return templates.TemplateResponse("approval_page.html", {"request": request})

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, user: str = Depends(get_current_user)):
    """Render the main dashboard page with real data"""
    # Fetch summary metrics
    request_counts = await get_request_counts()
    suppliers = await get_all_suppliers(None)  # None for db, as get_all_suppliers expects it
    pending_approvals = await fetch_pending_requests_service()

    summary = {
        "files_uploaded": request_counts["total"],
        "pending_approvals": len(pending_approvals),
        "requests": request_counts["total"],
        "suppliers": len(suppliers),
        "today": request_counts["today"],
        "completed": request_counts["completed"],
        "pending": request_counts["pending"]
    }

    # Fetch recent activity: last 5 uploads/requests, sorted by CreatedDate desc
    recent_requests, _, _ = await get_data_import_requests(
        skip=0, limit=5, sort_field="CreatedDate", sort_direction="desc"
    )
    recent_activity = []
    for req in recent_requests:
        file_name = req.get("InputFileName", "Unknown file")
        user = req.get("CreatedBy", "Unknown user")
        created = req.get("CreatedDate")
        status = req.get("StatusObj", {}).get("StatusName", "Unknown")
        activity_str = f"{file_name} uploaded by {user} ({status}) on {created}" if created else f"{file_name} uploaded by {user} ({status})"
        recent_activity.append(activity_str)

    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "summary": summary, "recent_activity": recent_activity}
    )

@app.get("/graph")
async def graph_page(request: Request, user: str = Depends(get_current_user)):
    return templates.TemplateResponse("graph.html", {"request": request, "graph_url": GRAPH_URL})

@app.get("/task-monitor", response_class=HTMLResponse)
async def task_monitor_page(request: Request, user: str = Depends(get_current_user)):
    """Render the task monitoring page"""
    return templates.TemplateResponse("task_monitor.html", {"request": request})
