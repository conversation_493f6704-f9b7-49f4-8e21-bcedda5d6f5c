{% extends "base.html" %}

{% block title %}Data Import{% endblock %}
{% block extra_head %}
<style>
    /* Modern Data Import Page Styles */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .page-header h2 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.75rem;
        display: flex;
        align-items: center;
    }

    .page-header h2 i {
        margin-right: var(--spacing-sm);
        color: var(--primary);
    }

    .page-subtitle {
        color: var(--gray-600);
        font-size: 1rem;
        margin-top: var(--spacing-xs);
    }

    /* Center the main container */
    .main-index {
        display: flex;
        flex-direction: column;
        max-width: 800px;
        margin: 0 auto;
    }

    /* Enhanced Card for File Upload and Request Type */
    .card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: all var(--transition);
        border: 1px solid var(--gray-200);
        position: relative;
        overflow: hidden;
    }

    .card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(to bottom, var(--primary), var(--secondary));
        opacity: 0.8;
    }

    .card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    /* Form Section Styles */
    .form-section {
        transition: all var(--transition);
        position: relative;
    }

    .form-section.hidden {
        opacity: 0;
        height: 0;
        margin: 0;
        overflow: hidden;
        transform: translateY(10px);
    }

    .form-section.visible {
        opacity: 1;
        animation: fade-in 0.5s ease-out;
    }

    @keyframes fade-in {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Form Labels */
    .card label {
        font-weight: var(--font-weight-bold);
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--gray-700);
        font-size: 1.05rem;
        transition: all var(--transition-fast);
    }

    .required::after {
        content: " *";
        color: var(--danger);
        font-size: 1.2rem;
    }

    /* Form Controls */
    .card select,
    .card input[type="date"] {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-300);
        margin-bottom: var(--spacing-md);
        transition: all var(--transition-fast);
        background-color: white;
        box-shadow: var(--shadow-sm);
        color: var(--gray-800);
        height: 100%;
    }

    .card select:hover,
    .card input[type="date"]:hover {
        border-color: var(--primary-light);
    }

    .card select:focus,
    .card input[type="date"]:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        outline: none;
    }

    .card select:disabled {
        background-color: var(--gray-100);
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* Enhanced Date Input */
    .date-input-container {
        position: relative;
        width: 93%;
    }

    .date-input {
        width: 100%;
        cursor: pointer;
    }

    .date-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--primary);
        pointer-events: none;
        font-size: 1.1rem;
    }

    /* Form section with icon */
    .form-section-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .form-section-icon {
        margin-right: var(--spacing-sm);
        color: var(--primary);
        font-size: 1.2rem;
    }

    .form-section-help {
        font-size: 0.9rem;
        color: var(--gray-600);
        margin-top: 0;
        margin-bottom: var(--spacing-sm);
    }

    /* Enhanced Upload Button */
    #uploadBtn {
        padding: var(--spacing-md) var(--spacing-lg);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        cursor: pointer;
        width: 100%;
        font-size: 1.1rem;
        font-weight: var(--font-weight-bold);
        transition: all var(--transition);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        height: 54px;
        box-shadow: 0 4px 6px rgba(67, 97, 238, 0.2);
        position: relative;
        overflow: hidden;
        margin-top: var(--spacing-md);
    }

    #uploadBtn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: all 0.6s ease;
    }

    #uploadBtn:hover:not(:disabled)::before {
        left: 100%;
    }

    #uploadBtn:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(67, 97, 238, 0.25);
    }

    #uploadBtn:active:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(67, 97, 238, 0.2);
    }

    #uploadBtn:disabled {
        background: linear-gradient(135deg, var(--gray-400) 0%, var(--gray-500) 100%);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.7;
    }

    #uploadBtn i {
        font-size: 1.3rem;
    }

    /* Enhanced Response Message */
    #message {
        margin-top: var(--spacing-md);
        font-weight: var(--font-weight-bold);
        color: var(--danger);
        text-align: center;
        min-height: 24px;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
        transition: all var(--transition-fast);
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        animation: none;
    }

    #message.show-error {
        display: block;
        background-color: rgba(244, 67, 54, 0.05);
        border-left: 3px solid var(--danger);
        opacity: 1;
        transform: translateY(0);
        animation: fade-in 0.3s ease-out;
    }

    /* Enhanced Loader */
    #loader {
        margin-top: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        color: var(--primary);
        padding: var(--spacing-md);
        background-color: rgba(67, 97, 238, 0.05);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
    }

    #loader .spinner {
        width: 28px;
        height: 28px;
        border: 3px solid rgba(67, 97, 238, 0.2);
        border-top-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        box-shadow: 0 0 10px rgba(67, 97, 238, 0.1);
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Enhanced Modern Drag & Drop Area */
    .drag-drop-area {
        border: 2px dashed var(--gray-300);
        text-align: center;
        font-size: 1.1rem;
        color: var(--gray-600);
        background-color: rgba(248, 249, 250, 0.7);
        cursor: pointer;
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-md);
        width: 100%;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
        transition: all var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .drag-drop-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.03) 0%, rgba(76, 201, 240, 0.03) 100%);
        opacity: 0;
        transition: opacity var(--transition);
        z-index: 0;
    }

    .drag-drop-area > * {
        position: relative;
        z-index: 1;
    }

    .drag-drop-area i {
        font-size: 3rem;
        color: var(--gray-400);
        transition: all var(--transition);
        margin-bottom: var(--spacing-sm);
    }

    .drag-drop-area .file-types {
        font-size: 0.85rem;
        color: var(--gray-500);
        margin-top: var(--spacing-sm);
        background-color: var(--gray-100);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--border-radius);
        display: inline-block;
    }

    .drag-drop-area:hover {
        border-color: var(--primary);
        transform: translateY(-2px);
    }

    .drag-drop-area:hover::before {
        opacity: 1;
    }

    .drag-drop-area:hover i {
        color: var(--primary);
        transform: translateY(-5px) scale(1.1);
    }

    .drag-drop-area.dragover {
        background-color: rgba(67, 97, 238, 0.05);
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15), var(--shadow);
        transform: translateY(-4px);
    }

    .drag-drop-area.has-file {
        border-color: var(--success);
        background-color: rgba(76, 175, 80, 0.05);
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15), var(--shadow-sm);
    }

    .drag-drop-area.has-file i {
        color: var(--success);
    }

    .drag-drop-area .file-size {
        font-size: 0.9rem;
        color: var(--gray-600);
        margin-top: var(--spacing-xs);
        background-color: rgba(76, 175, 80, 0.1);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--border-radius);
        display: inline-block;
    }

    /* Ultra Modern Success Popup with Neumorphic Design */
    .popup {
        position: fixed;
        top: 20px;
        right: 20px;
        color: var(--gray-800);
        padding: 24px;
        border-radius: 20px;
        z-index: 9999;
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.08),
            0 5px 15px rgba(0, 0, 0, 0.03),
            inset 0 -2px 0 rgba(0, 0, 0, 0.05);
        width: 460px;
        font-family: var(--font-family);
        transform: translateX(400px) translateY(-10px);
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        background: #ffffff;
        border: 1px solid rgba(255, 255, 255, 0.8);
        overflow: hidden;
    }

    .popup::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 8px;
        height: 100%;
        background: linear-gradient(to bottom, #4CAF50, #2E7D32);
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
    }

    .popup::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
        pointer-events: none;
        z-index: -1;
    }

    .popup.show {
        transform: translateX(0) translateY(0);
        animation: popup-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    @keyframes popup-appear {
        0% { transform: translateX(400px) translateY(-10px); opacity: 0; }
        60% { transform: translateX(-15px) translateY(5px); opacity: 1; }
        80% { transform: translateX(5px) translateY(-2px); }
        100% { transform: translateX(0) translateY(0); }
    }

    .popup.hidden {
        display: none;
    }

    .popup .popup-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 16px;
        color: #2E7D32;
        font-weight: 600;
        font-size: 1.25rem;
    }

    .popup .popup-header i {
        font-size: 1.5rem;
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        color: white;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow:
            0 6px 12px rgba(76, 175, 80, 0.2),
            0 2px 4px rgba(76, 175, 80, 0.1);
    }

    .popup .popup-message {
        display: block;
        margin-bottom: 16px;
        line-height: 1.6;
        color: #424242;
        font-size: 1.05rem;
        padding: 4px 0;
        border-radius: 4px;
    }

    .popup .popup-close {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        font-size: 1rem;
        color: #757575;
        position: absolute;
        top: 12px;
        right: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow:
            0 2px 5px rgba(0, 0, 0, 0.05),
            0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .popup .popup-close:hover {
        color: #E53935;
        background-color: rgba(229, 57, 53, 0.1);
        transform: scale(1.1) rotate(90deg);
    }

    /* Progress bar inside popup */
    .popup-progress {
        width: 100%;
        height: 6px;
        background-color: #f5f5f5;
        border-radius: 100px;
        overflow: hidden;
        margin-top: 16px;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .popup-progress-bar {
        width: 100%;
        height: 100%;
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        animation: progressBar 3s cubic-bezier(0.1, 0.9, 0.2, 1) forwards;
        border-radius: 100px;
        position: relative;
        overflow: hidden;
    }

    .popup-progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.4) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        animation: shimmer 1.5s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    @keyframes progressBar {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* Confirmation Modal Styles - Following Approval Page Pattern */
    .confirmation-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        animation: fadeIn 0.3s ease;
        backdrop-filter: blur(5px);
    }

    .confirmation-modal-overlay.hidden {
        display: none;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }

    .confirmation-modal-content {
        background-color: white;
        border-radius: 12px;
        width: 500px;
        max-width: 90%;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
        animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    @keyframes slideIn {
        from { transform: translateY(-70px) scale(0.95); opacity: 0; }
        to { transform: translateY(0) scale(1); opacity: 1; }
    }

    @keyframes scaleDown {
        from {
            transform: scale(1);
            opacity: 1;
        }
        to {
            transform: scale(0.95);
            opacity: 0;
        }
    }

    .confirmation-modal-header {
        padding: 25px 30px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        display: flex;
        align-items: center;
        gap: 15px;
        position: relative;
        overflow: hidden;
    }

    .confirmation-modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    }

    .confirmation-modal-header i {
        font-size: 32px;
        z-index: 1;
        text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.2);
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .confirmation-modal-header h3 {
        margin: 0;
        font-size: 22px;
        font-weight: 600;
        z-index: 1;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        color: white;
    }

    .confirmation-modal-body {
        padding: 30px;
        font-size: 16px;
        color: #495057;
        line-height: 1.6;
        background-color: #f9fafc;
        border-bottom: 1px solid #eef1f6;
        max-height: 400px;
        overflow-y: auto;
    }

    .confirmation-modal-body p {
        margin: 0 0 10px 0;
    }

    .upload-details-container {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        margin: 15px 0;
        border: 1px solid #e2e8f0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .confirmation-modal-actions {
        padding: 20px 30px;
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        background-color: white;
    }

    .confirmation-modal-cancel {
        padding: 12px 20px;
        background-color: #f1f5f9;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        color: #495057;
        transition: all 0.2s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .confirmation-modal-cancel:hover {
        background-color: #e2e8f0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .confirmation-modal-proceed {
        padding: 12px 24px;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: 5px;
        box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
    }

    .confirmation-modal-proceed::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%);
        transition: all 0.8s ease;
    }

    .confirmation-modal-proceed:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
    }

    .confirmation-modal-proceed:hover::after {
        left: 100%;
    }

    /* Prevent body scroll when modal is active */
    body.modal-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }

    /* Form Header */
    .form-header {
        margin-bottom: var(--spacing-md);
    }

    .form-header h3 {
        font-size: 1.3rem;
        color: var(--gray-800);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .form-header h3 i {
        color: var(--primary);
    }

    /* Enhanced Form Steps Indicator */
    .form-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
        padding: var(--spacing-md) 0 var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
    }

    .form-steps::before {
        content: '';
        position: absolute;
        top: 36px;
        left: 12%;
        right: 12%;
        height: 2px;
        background-color: var(--gray-200);
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        width: 25%;
    }

    .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--gray-200);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--spacing-sm);
        transition: all var(--transition);
        box-shadow: var(--shadow-sm);
        border: 2px solid white;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.85rem;
        color: var(--gray-600);
        transition: all var(--transition);
        text-align: center;
        font-weight: var(--font-weight-normal);
    }

    .step.active .step-number {
        background-color: var(--primary);
        color: white;
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    }

    .step.active .step-label {
        color: var(--primary);
        font-weight: var(--font-weight-bold);
    }

    .step.completed .step-number {
        background-color: var(--success);
        color: white;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
    }

    .step.completed .step-label {
        color: var(--success);
        font-weight: var(--font-weight-bold);
    }

    .step.completed .step-number::after {
        content: '\f00c';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
    }
    .request-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: box-shadow var(--transition);
    }

    .request-container:hover {
        box-shadow: var(--shadow-md);
    }
</style>
{% endblock %}

{% block content %}
<div class="request-container">
    <div class="main-index">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h2><i class="fas fa-file-upload"></i> Data Import</h2>
                <div class="page-subtitle">Upload your data files for processing</div>
            </div>
        </div>

        <!-- File Upload Form Header -->
        <div class="form-header">
            <h3><i class="fas fa-cloud-upload-alt"></i> File Upload Form</h3>
        </div>

        <div class="card">
            <!-- Form Steps Indicator -->
            <div class="form-steps">
                <div class="step active" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-label">Supplier</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-label">Request Type</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-label">File</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-label">Upload</div>
                </div>
            </div>

            <!-- Supplier Selection -->
            <div class="form-section supplier-box visible">
                <div class="form-section-header">
                    <i class="fas fa-building form-section-icon"></i>
                    <label for="supplierId" class="required">Select Supplier</label>
                </div>
                <p class="form-section-help">Choose the supplier for this data import</p>
                <select id="supplierId">
                    <option value="">-- Loading Suppliers... --</option>
                </select>
            </div>

            <!-- Request Type Section (Initially Hidden) -->
            <div class="form-section request-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-file-import form-section-icon"></i>
                    <label for="requestId" class="required">Select Request Type</label>
                </div>
                <p class="form-section-help">Choose the type of data you're importing</p>
                <select id="requestId" disabled>
                    <option value="">-- Select Request Type --</option>
                    <option value="1">All Importing</option>
                    <option value="2">Product Importing</option>
                    <option value="3">Price Importing</option>
                    <option value="4">Stock Importing</option>
                </select>
            </div>

            <!-- File Upload Section (Initially Hidden) -->
            <div class="form-section upload-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-file-excel form-section-icon"></i>
                    <label for="fileInput" class="required">Choose a File</label>
                </div>
                <p class="form-section-help">Select the data file you want to upload</p>
                <!-- Drag & Drop Area -->
                <div class="drag-drop-area" id="dragDropArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <div>
                        Drag & Drop a file here<br>
                        <strong>or click to browse</strong>
                    </div>
                    <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
                </div>
                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" style="display: none;">
            </div>

            <!-- Created Date Picker (Initially Hidden) -->
            <div class="form-section date-box hidden">
                <div class="form-section-header">
                    <i class="fas fa-calendar-alt form-section-icon"></i>
                    <label for="datePicker">Select Created Date (Optional)</label>
                </div>
                <p class="form-section-help">Choose a specific date for this data import</p>
                <div class="date-input-container">
                    <input type="date" id="datePicker" class="date-input" placeholder="YYYY-MM-DD">
                </div>
            </div>

            <!-- Upload Button -->
            <button id="uploadBtn" disabled>
                <i class="fas fa-upload"></i> Upload File
            </button>

            <!-- Loader (Initially hidden) -->
            <div id="loader" style="display: none;">
                <div class="spinner"></div>
                <span>Uploading file, please wait...</span>
            </div>

            <!-- Response Message (for errors) -->
            <p id="message"></p>
        </div>

        <!-- Upload Confirmation Modal -->
        <div id="confirmationModal" class="confirmation-modal-overlay hidden">
            <div class="confirmation-modal-content">
                <div class="confirmation-modal-header">
                    <i class="fas fa-exclamation-circle"></i>
                    <h3>Confirm Upload</h3>
                </div>
                <div class="confirmation-modal-body">
                    <p>Please review the upload details before proceeding:</p>
                    <div id="uploadDetails" class="upload-details-container">
                        <!-- Upload details will be populated here -->
                    </div>
                    <p style="margin-top: 16px;">
                        <i class="fas fa-info-circle" style="color: #4361ee; margin-right: 8px;"></i>
                        This action will upload and process your file. Make sure all details are correct.
                    </p>
                </div>
                <div class="confirmation-modal-actions">
                    <button id="confirmationCancel" class="confirmation-modal-cancel">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button id="confirmationProceed" class="confirmation-modal-proceed">
                        <i class="fas fa-check"></i> Proceed with Upload
                    </button>
                </div>
            </div>
        </div>

        <!-- Ultra Modern Success Popup with Neumorphic Design -->
        <div id="successPopup" class="popup hidden">
            <div class="popup-header">
                <i class="fas fa-check-circle"></i>
                <span>Upload Successful</span>
            </div>
            <button class="popup-close"><i class="fas fa-times"></i></button>
            <div class="popup-message"></div>
            <div class="popup-progress">
                <div class="popup-progress-bar"></div>
            </div>
        </div>
    </div>
</div>

{% block extra_scripts %}
<script>
    $(document).ready(function () {
        let supplierMap = {}; // Store Supplier ID -> Name mapping
        let currentStep = 1; // Track current form step
        let suppliersLoaded = false; // Flag to prevent duplicate supplier loading
        let isInitialized = false; // Flag to prevent duplicate initialization

        // Prevent multiple initializations
        if (isInitialized) {
            return;
        }
        isInitialized = true;

        // Make sure the popups are properly initialized
        $("#successPopup").removeClass("show").addClass("hidden");
        $("#confirmationModal").addClass("hidden");

        // Reset the form to its initial state (without reloading suppliers)
        function resetForm(skipSupplierReset = false) {
            if (!skipSupplierReset) {
                $("#supplierId").val("");
            }
            $("#requestId").val("").prop("disabled", true);
            $("#fileInput").val("");
            // Reset the date picker
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset form sections
            $(".form-section").removeClass("visible").addClass("hidden");
            $(".supplier-box").removeClass("hidden").addClass("visible");

            // Reset steps
            updateFormSteps(1);

            // Clear message
            $("#message").text("").removeClass("show-error");
            $("#loader").hide();

            // Reset drag-drop area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);
        }

        // Update form steps indicator
        function updateFormSteps(step) {
            currentStep = step;

            // Reset all steps
            $(".step").removeClass("active completed");

            // Mark current step as active
            $(`#step${step}`).addClass("active");

            // Mark previous steps as completed
            for (let i = 1; i < step; i++) {
                $(`#step${i}`).removeClass("active").addClass("completed");
            }
        }

        // Immediately reset the form on page load (handles soft reloads)
        resetForm();

        // Validate file extensions
        function isValidFileType(fileName) {
            let allowedExtensions = ['xlsx', 'xls', 'csv'];
            let fileExtension = fileName.split('.').pop().toLowerCase();
            return allowedExtensions.includes(fileExtension);
        }

        // Enable Upload button if a request is selected and a file is chosen
        function checkEnableUploadButton() {
            let requestSelected = $("#requestId").val();
            let fileSelected = $("#fileInput")[0].files.length > 0;
            $("#uploadBtn").prop("disabled", !(requestSelected && fileSelected));

            // Update step 4 if all conditions are met
            if (requestSelected && fileSelected) {
                updateFormSteps(4);
            }
        }

        // Enhanced loader functions for the form loader
        function showLoader() {
            // Show only the form loader, not the main page loader
            $("#loader").fadeIn(300);
            $("#uploadBtn").prop("disabled", true);
        }

        function hideLoader() {
            // Hide the form loader
            $("#loader").fadeOut(300);

            // Re-enable the upload button if conditions are met
            checkEnableUploadButton();
        }

        // Fetch suppliers from the server (with duplicate prevention)
        function fetchSuppliers() {
            // Prevent duplicate API calls
            if (suppliersLoaded) {
                return;
            }

            suppliersLoaded = true; // Set flag immediately to prevent race conditions

            $.ajax({
                url: "/api/suppliers",
                type: "GET",
                success: function (response) {
                    let supplierDropdown = $("#supplierId");
                    supplierDropdown.empty();
                    supplierDropdown.append('<option value="">-- Select Supplier --</option>');

                    // Sort suppliers alphabetically by display name
                    response.sort((a, b) => a.display_name.localeCompare(b.display_name));

                    response.forEach(function (supplier) {
                        supplierMap[supplier.supplier_id] = supplier.display_name;
                        supplierDropdown.append(`<option value="${supplier.supplier_id}">${supplier.display_name}</option>`);
                    });
                },
                error: function () {
                    // Reset flag on error so user can retry
                    suppliersLoaded = false;
                    $("#supplierId").html('<option value="">-- Failed to Load Suppliers --</option>');
                    $("#message").text("Failed to load suppliers. Please refresh the page.").addClass("show-error");
                }
            });
        }

        // Supplier change event: reveal request type section and reset subsequent steps
        $("#supplierId").change(function () {
            let supplierSelected = $(this).val();

            // Reset subsequent form sections regardless of selection
            $("#requestId").val("").prop("disabled", true);
            $("#fileInput").val("");
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset file upload area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);

            // Hide subsequent sections
            $(".upload-box, .date-box").removeClass("visible").addClass("hidden");

            // Clear any error messages
            $("#message").text("").removeClass("show-error");

            if (supplierSelected) {
                // Show request type section with animation
                $(".request-box").removeClass("hidden").addClass("visible");
                $("#requestId").prop("disabled", false);

                // Update step indicator
                updateFormSteps(2);
            } else {
                resetForm();
            }
        });

        // Request type change event: reveal file upload section and reset subsequent steps
        $("#requestId").change(function () {
            let requestSelected = $(this).val();

            // Reset file upload related fields
            $("#fileInput").val("");
            $("#datePicker").val("");
            $("#uploadBtn").prop("disabled", true);

            // Reset file upload area
            $("#dragDropArea").removeClass("has-file dragover").html(`
                <i class="fas fa-cloud-upload-alt"></i>
                <div>
                    Drag & Drop a file here<br>
                    <strong>or click to browse</strong>
                </div>
                <div class="file-types">Accepted formats: .xlsx, .xls, .csv</div>
            `);

            // Clear any error messages
            $("#message").text("").removeClass("show-error");

            if (requestSelected) {
                // Show file upload and date sections with animation
                $(".upload-box, .date-box").removeClass("hidden").addClass("visible");

                // Update step indicator
                updateFormSteps(3);
            } else {
                // Hide file upload and date sections
                $(".upload-box, .date-box").removeClass("visible").addClass("hidden");

                // Update step indicator back to supplier selection
                updateFormSteps(2);
            }

            checkEnableUploadButton();
        });

        // File input change event: validate file and update drag-drop area
        $("#fileInput").change(function () {
            let file = this.files[0];
            if (file) {
                if (!isValidFileType(file.name)) {
                    $("#message").text("Invalid file type! Please select an Excel or CSV file.").addClass("show-error");
                    $("#fileInput").val("");
                    $("#uploadBtn").prop("disabled", true);
                    $("#dragDropArea").removeClass("has-file");
                    return;
                }

                $("#message").text("").removeClass("show-error");

                // Update drag-drop area with file name
                $("#dragDropArea").addClass("has-file").html(`
                    <i class="fas fa-file-alt"></i>
                    <div>${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                `);
            }

            checkEnableUploadButton();
        });

        // Format file size for display
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Enhanced Drag & Drop Functionality (with proper event handler management)
        function initializeDragDrop() {
            let dropArea = $("#dragDropArea");

            // Remove any existing event handlers to prevent duplicates
            dropArea.off("click dragover dragleave drop");

            dropArea.on("click", function () {
                $("#fileInput").click();
            });

            dropArea.on("dragover", function (e) {
                e.preventDefault();
                $(this).addClass("dragover");
            });

            dropArea.on("dragleave", function () {
                $(this).removeClass("dragover");
            });

            dropArea.on("drop", function (e) {
                e.preventDefault();
                $(this).removeClass("dragover");

                let files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    let file = files[0];
                    if (!isValidFileType(file.name)) {
                        $("#message").text("Invalid file type! Please select an Excel or CSV file.").addClass("show-error");
                        return;
                    }

                    // Hide any previous error messages
                    $("#message").text("").removeClass("show-error");

                    // Update the hidden file input
                    $("#fileInput")[0].files = files;

                    // Update drag-drop area with file name
                    $(this).addClass("has-file").html(`
                        <i class="fas fa-file-alt"></i>
                        <div>${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    `);

                    checkEnableUploadButton();
                }
            });
        }

        // Ultra modern success popup with enhanced animation
        function showPopup(message, requestId) {
            console.log("Showing popup with message:", message, "and request ID:", requestId);

            // Extract filename from message if possible
            let filename = "";
            if (message.includes("'")) {
                filename = message.split("'")[1].split("'")[0];
            }

            // Format the message similar to the screenshot
            let formattedMessage = `
                <div style="font-size: 1.05rem; margin-bottom: 8px;">
                    The file <span style="font-weight: 500;">'${filename}'</span> was uploaded and processed successfully.
                </div>
                <div style="display: flex; align-items: center; margin-top: 8px; margin-bottom: 4px;">
                    <span style="font-size: 0.9rem; color: #616161; margin-right: 8px;">Request ID:</span>
                    <span style="font-weight: 600; color: #2E7D32; font-size: 0.95rem;">${requestId}</span>
                </div>
            `;

            // Set the formatted message
            $("#successPopup .popup-message").html(formattedMessage);

            // Reset the progress bar with a slight delay for better animation
            setTimeout(function() {
                $("#successPopup .popup-progress").html('<div class="popup-progress-bar"></div>');
            }, 100);

            // Make sure the popup is visible in the DOM
            $("#successPopup").css("display", "block");

            // Show popup with animation
            $("#successPopup").removeClass("hidden").addClass("show");

            console.log("Popup element:", $("#successPopup")[0]);
            console.log("Popup classes:", $("#successPopup").attr("class"));

            // Auto dismiss after 5 seconds (no page refresh needed)
            setTimeout(function() {
                $("#successPopup").removeClass("show");
                setTimeout(function() {
                    $("#successPopup").addClass("hidden");
                    // Reset form instead of refreshing page
                    resetForm(true); // Skip supplier reset since they're already loaded
                }, 400);
            }, 5000);
        }

        // Close popup on click of close button with enhanced animation (no page refresh)
        function initializePopupHandlers() {
            $("#successPopup .popup-close").off("click").on("click", function() {
                // Add a subtle scale effect when closing
                $("#successPopup").css("transform", "scale(0.98)");
                $("#successPopup").removeClass("show");

                setTimeout(function() {
                    $("#successPopup").addClass("hidden");
                    // Reset form instead of refreshing page to avoid duplicate API calls
                    resetForm(true); // Skip supplier reset since they're already loaded
                }, 400);
            });
        }

        // Enhanced upload button click event - now shows confirmation modal
        function initializeUploadButton() {
            $("#uploadBtn").off("click").on("click", function () {
            let file = $("#fileInput")[0].files[0];
            let supplierId = $("#supplierId").val();
            let requestId = $("#requestId").val();
            let supplierName = supplierMap[supplierId] || "";

                // Validate inputs
                if (!file) {
                    $("#message").text("Please select a file.").addClass("show-error");
                    return;
                }
                if (!supplierId) {
                    $("#message").text("Please select a supplier.").addClass("show-error");
                    return;
                }
                if (!requestId) {
                    $("#message").text("Please select a request type.").addClass("show-error");
                    return;
                }

                // Clear any previous error messages
                $("#message").text("").removeClass("show-error");

                // Show confirmation modal with upload details
                showConfirmationModal(file, supplierId, supplierName, requestId);
            });
        }

        // Function to show confirmation modal with upload details
        function showConfirmationModal(file, supplierId, supplierName, requestId) {
            // Get request type name
            let requestTypeName = $("#requestId option:selected").text();
            let selectedDate = $("#datePicker").val();

            // Format file size
            let fileSize = formatFileSize(file.size);

            // Build upload details HTML
            let detailsHtml = `
                <div style="display: grid; grid-template-columns: 120px 1fr; gap: 12px; font-size: 0.95rem;">
                    <div style="font-weight: 600; color: #495057;">File:</div>
                    <div style="color: #212529; word-break: break-word;">${file.name} <span style="color: #6c757d;">(${fileSize})</span></div>

                    <div style="font-weight: 600; color: #495057;">Supplier:</div>
                    <div style="color: #212529;">${supplierName}</div>

                    <div style="font-weight: 600; color: #495057;">Request Type:</div>
                    <div style="color: #212529;">${requestTypeName}</div>

                    ${selectedDate ? `
                        <div style="font-weight: 600; color: #495057;">Date:</div>
                        <div style="color: #212529;">${selectedDate}</div>
                    ` : ''}
                </div>
            `;

            // Update modal content
            $("#uploadDetails").html(detailsHtml);

            // Prevent body scroll
            $("body").addClass("modal-open");

            // Show the modal with animation
            $("#confirmationModal").removeClass("hidden");
        }

        // Function to close confirmation modal with animation
        function closeConfirmationModal() {
            const modal = $("#confirmationModal")[0];
            const content = modal.querySelector('.confirmation-modal-content');

            // Restore body scroll
            $("body").removeClass("modal-open");

            // Add fade out animation to overlay
            modal.style.animation = 'fadeOut 0.3s ease forwards';

            // Add scale down animation to content
            if (content) {
                content.style.animation = 'scaleDown 0.3s ease forwards';
            }

            // Hide the modal after animation completes
            setTimeout(function() {
                $("#confirmationModal").addClass("hidden");
                // Reset animations for next time
                modal.style.animation = '';
                if (content) {
                    content.style.animation = '';
                }
            }, 300);
        }

        // Confirmation modal event handlers
        $("#confirmationCancel").on("click", function() {
            closeConfirmationModal();
        });

        // Close modal when clicking on overlay (outside the modal content)
        $("#confirmationModal").on("click", function(e) {
            if (e.target === this) {
                closeConfirmationModal();
            }
        });

        // Prevent modal from closing when clicking inside the modal content
        $(".confirmation-modal-content").on("click", function(e) {
            e.stopPropagation();
        });

        // Close modal with ESC key
        $(document).on("keydown", function(e) {
            if (e.key === "Escape" && !$("#confirmationModal").hasClass("hidden")) {
                closeConfirmationModal();
            }
        });

        // Proceed with upload button click event
        $("#confirmationProceed").on("click", function() {
            // Hide the confirmation modal first
            closeConfirmationModal();

            // Proceed with the actual upload after a short delay
            setTimeout(function() {
                performUpload();
            }, 100);
        });

        // Function to perform the actual upload
        function performUpload() {
            let file = $("#fileInput")[0].files[0];
            let supplierId = $("#supplierId").val();
            let requestId = $("#requestId").val();
            let supplierName = supplierMap[supplierId] || "";

            // Show loader and disable button
            showLoader();

            // Prepare form data
            let formData = new FormData();
            formData.append("file", file);
            formData.append("SupplierID", supplierId);
            formData.append("SupplierName", supplierName);
            formData.append("RequestTypeId", requestId);

            // Add CreatedDate if selected (optional)
            let selectedCreatedDate = $("#datePicker").val();
            if (selectedCreatedDate) {
                formData.append("CreatedDate", selectedCreatedDate);
            }

            // Send AJAX request
            $.ajax({
                url: "/api/files/new-upload",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    hideLoader();

                    // Check if the response indicates an error
                    if(response.status_code !== 200){
                         $("#message").text(response.message).addClass("show-error");
                         $("#uploadBtn").prop("disabled", false);
                         return;
                    }

                    // Show success popup and reset form (without page refresh)
                    console.log("Upload response:", response); // Debug log

                    try {
                        showPopup(response.data.responses, response.data.task_id);
                    } catch (error) {
                        // Fallback if the response structure is different
                        console.error("Error accessing response data:", error);
                        showPopup("File uploaded successfully", "N/A");
                    }

                    // Reset form but keep suppliers loaded to avoid duplicate API calls
                    resetForm(true);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    hideLoader();

                    var errorMessage = "Error uploading file.";
                    // If the API returns a JSON error response with a "message" field, use that
                    if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        errorMessage = jqXHR.responseJSON.message;
                    }

                    $("#message").text(errorMessage).addClass("show-error");
                    $("#uploadBtn").prop("disabled", false);
                }
            });
        }

        // Initialize all components
        function initializeComponents() {
            initializeDragDrop();
            initializePopupHandlers();
            initializeUploadButton();
        }

        // Initialize the page
        initializeComponents();

        // Fetch suppliers on page load (only once)
        fetchSuppliers();
    });
</script>
{% endblock %}
{% endblock %}
